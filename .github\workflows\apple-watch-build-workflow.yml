# Dr. <PERSON><PERSON>cle Apple Watch Build & Deploy Workflow
# Builds and deploys the standalone Dr. Muscle Watch app to TestFlight
# Integrated with existing CI/CD patterns and Scaleway Mac infrastructure

name: Apple Watch Build & Deploy

concurrency:
  group: watch-build-${{ github.ref }}
  cancel-in-progress: true

permissions:
  contents: read
  issues: write

on:
  workflow_dispatch: # Manual trigger

# Dr. Muscle Watch App Configuration
env:
  # Project Configuration
  PROJECT_NAME: "DrMuscleWatchApp.xcodeproj"
  SCHEME_NAME: "DrMuscleWatchApp"
  APP_NAME: "DrMuscleWatchApp"
  PROJECT_PATH: "DrMuscleWatch/v1/DrMuscleWatchApp"

  # Bundle Identifiers (following <PERSON><PERSON> <PERSON><PERSON><PERSON> pattern)
  WATCH_APP_BUNDLE_ID: "com.drmaxmuscle.dr_max_muscle.watchapp"

  # Provisioning Profile Names (standalone watchOS app - no extension needed)
  WATCH_APP_PROVISIONING_PROFILE_NAME: "Dr_<PERSON><PERSON><PERSON>_Watch_App_Store_Profile"

jobs:
  # Centralized setup job for shared dependencies and version generation
  setup:
    name: Setup & Version Generation
    runs-on: ubicloud-standard-2
    outputs:
      start-time: ${{ steps.start-time.outputs.start-time }}
      version-code: ${{ steps.version.outputs.version_code }}
      version-name: ${{ steps.version.outputs.version_name }}
      assembly-version: ${{ steps.version.outputs.assembly_version }}
    steps:
    - name: Record workflow start time
      id: start-time
      run: |
        START_TIME=$(date +%s)
        echo "start-time=$START_TIME" >> $GITHUB_OUTPUT
        echo "Workflow started at: $(date -d @$START_TIME)"

    - uses: actions/checkout@v4

    - name: Generate unified version for Watch App
      id: version
      run: |
        # Get current date components for clean versioning
        YEAR=$(date +%Y)
        MONTH=$(date +%m)
        DAY=$(date +%d)
        BUILD_NUMBER=$GITHUB_RUN_NUMBER

        # Create clean 3-part version: 3.YYMM.DDXX (XX = last 2 digits of run)
        YY=$(echo $YEAR | tail -c 3)  # Last 2 digits of year (25 for 2025)
        YYMM="${YY}$(printf "%02d" $((10#$MONTH)))"  # YYMM format (2506 for June 2025)
        RUN_LAST_TWO=$(printf "%02d" $((BUILD_NUMBER % 100)))  # Last 2 digits of run (12 for run 112)
        DD_XX="$(printf "%02d" $((10#$DAY)))${RUN_LAST_TWO}"  # DDXX (1612 for 16th day, run 112)

        # Single clean 3-part version for Watch app
        CLEAN_VERSION="3.${YYMM}.${DD_XX}"

        echo "version_code=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "version_name=$CLEAN_VERSION" >> $GITHUB_OUTPUT
        echo "assembly_version=$CLEAN_VERSION" >> $GITHUB_OUTPUT

        echo "Using clean 3-part version format for Watch app:"
        echo "  Version: $CLEAN_VERSION (3.YYMM.DDXX where XX = last 2 digits of run)"
        echo "  Example: 3.2506.1212 = June 12th, 2025, run #112 → XX=12"

  # Check if runner is already available before waking up server
  check-runner-availability:
    name: Check Runner Availability
    runs-on: ubicloud-standard-2
    needs: setup
    outputs:
      runner-available: ${{ steps.check-runner-status.outputs.runner-available }}
      skip-wake-up: ${{ steps.check-runner-status.outputs.skip-wake-up }}
    steps:
    - name: Check if runner is already available
      id: check-runner-status
      run: |
        echo "🔍 Checking if Scaleway runner is already available..."

        # Install sshpass for SSH connectivity check
        sudo apt-get update -qq && sudo apt-get install -y sshpass

        # SSH connection details from GitHub secrets
        SSH_HOST="${{ secrets.SCALEWAY_MAC_SSH_HOST }}"
        SSH_USER="${{ secrets.SCALEWAY_MAC_SSH_USER }}"
        SSH_PASS="${{ secrets.SCALEWAY_MAC_SSH_PASSWORD }}"
        SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=10"

        # Check if SSH credentials are available
        if [ -z "$SSH_HOST" ] || [ -z "$SSH_USER" ] || [ -z "$SSH_PASS" ]; then
          echo "⚠️ SSH credentials not available - will perform full wake-up"
          echo "runner-available=false" >> $GITHUB_OUTPUT
          echo "skip-wake-up=false" >> $GITHUB_OUTPUT
        else
          # Quick check if runner process is already running
          echo "🔍 Checking if runner process is active..."
          RUNNER_CHECK=$(sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "ps aux | grep 'Runner.Listener' | grep -v grep" 2>/dev/null || echo "No runner process found")

          if echo "$RUNNER_CHECK" | grep -q "Runner.Listener"; then
            echo "✅ Runner process is already running - skipping wake-up and SSH setup"
            echo "Runner process: $RUNNER_CHECK"
            echo "runner-available=true" >> $GITHUB_OUTPUT
            echo "skip-wake-up=true" >> $GITHUB_OUTPUT
          else
            echo "⚠️ Runner process not found - will perform wake-up and SSH setup"
            echo "runner-available=false" >> $GITHUB_OUTPUT
            echo "skip-wake-up=false" >> $GITHUB_OUTPUT
          fi
        fi

  # Wake up Scaleway Mac mini only if runner is not already available
  wake-scaleway-runner:
    name: Wake Up Scaleway Mac Runner
    if: always()
    runs-on: ubicloud-standard-2
    needs: [setup, check-runner-availability]
    outputs:
      runner-status: ${{ steps.check-runner.outputs.runner-status }}
    steps:
    - name: Check if wake-up is needed
      id: wake-up-check
      run: |
        if [ "${{ needs.check-runner-availability.outputs.skip-wake-up }}" = "true" ]; then
          echo "✅ Runner is already available - skipping wake-up process"
          echo "skip-wake-up=true" >> $GITHUB_OUTPUT
        else
          echo "🔄 Runner not available - proceeding with wake-up process"
          echo "skip-wake-up=false" >> $GITHUB_OUTPUT
        fi

    - name: Wake up Scaleway Mac mini
      if: steps.wake-up-check.outputs.skip-wake-up == 'false'
      env:
        SCW_ACCESS_KEY: ${{ secrets.SCW_ACCESS_KEY }}
        SCW_SECRET_KEY: ${{ secrets.SCW_SECRET_KEY }}
        SCW_DEFAULT_PROJECT_ID: ${{ secrets.SCW_DEFAULT_PROJECT_ID }}
        SCW_DEFAULT_ORGANIZATION_ID: ${{ secrets.SCW_DEFAULT_ORGANIZATION_ID }}
        SCW_DEFAULT_REGION: ${{ secrets.SCW_DEFAULT_REGION }}
        SCW_DEFAULT_ZONE: ${{ secrets.SCW_DEFAULT_ZONE }}
        SCALEWAY_SERVER_ID: ${{ secrets.SCALEWAY_SERVER_ID }}
      run: |
        echo "🚀 Attempting to wake up Scaleway Mac mini for Watch app build..."

        # Set default zone for Apple Silicon API calls
        if [ -n "$SCW_DEFAULT_ZONE" ]; then
          API_ZONE="$SCW_DEFAULT_ZONE"
          echo "✅ Using zone: $API_ZONE"
        else
          API_ZONE="fr-par-3"
          echo "✅ Using default zone: $API_ZONE"
        fi

        # Set Apple Silicon API endpoint
        API_ENDPOINT="https://api.scaleway.com/apple-silicon/v1alpha1/zones/$API_ZONE"
        echo "🔧 Using Apple Silicon API endpoint: $API_ENDPOINT"

        # Check current server status
        echo "🔍 Checking Apple Silicon Mac mini status..."
        SERVER_RESPONSE=$(curl -s -H "X-Auth-Token: $SCW_SECRET_KEY" \
          -H "Content-Type: application/json" \
          "$API_ENDPOINT/servers/$SCALEWAY_SERVER_ID")

        if echo "$SERVER_RESPONSE" | jq -e '.status' >/dev/null 2>&1; then
          SERVER_STATUS=$(echo "$SERVER_RESPONSE" | jq -r '.status')
          SERVER_NAME=$(echo "$SERVER_RESPONSE" | jq -r '.name')
          echo "✅ Found Apple Silicon server '$SERVER_NAME' with status: $SERVER_STATUS"
        else
          echo "❌ Failed to get server status:"
          echo "$SERVER_RESPONSE"
          exit 1
        fi

        echo "📊 Current server status: $SERVER_STATUS"

        # Skip reboot if server is already ready
        if [ "$SERVER_STATUS" = "ready" ]; then
          echo "✅ Server is already ready, skipping reboot to avoid service disruption"
        else
          echo "⚠️ Server not ready (status: $SERVER_STATUS), but skipping reboot for now"
          echo "ℹ️ If runner issues persist, manual reboot may be needed"
        fi

    - name: Wait for GitHub Actions runner to come online
      id: check-runner
      if: steps.wake-up-check.outputs.skip-wake-up == 'false'
      run: |
        echo "⏳ Waiting for GitHub Actions runner to come online..."
        echo "ℹ️ Ensuring runner service is properly configured and running"

        # Server should already be ready, minimal wait for SSH
        echo "⏱️ Waiting 10 seconds for SSH to be ready..."
        sleep 10

        # SSH connection details from GitHub secrets
        SSH_HOST="${{ secrets.SCALEWAY_MAC_SSH_HOST }}"
        SSH_USER="${{ secrets.SCALEWAY_MAC_SSH_USER }}"
        SSH_PASS="${{ secrets.SCALEWAY_MAC_SSH_PASSWORD }}"
        SSH_OPTIONS="-o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -o ConnectTimeout=30"

        # Validate SSH credentials are available
        if [ -z "$SSH_HOST" ] || [ -z "$SSH_USER" ] || [ -z "$SSH_PASS" ]; then
          echo "❌ SSH credentials not configured in GitHub secrets"
          echo "runner-status=ssh-unavailable" >> $GITHUB_OUTPUT
          exit 0
        fi

        # Install sshpass for password authentication
        echo "📦 Installing sshpass for SSH password authentication..."
        sudo apt-get update -qq && sudo apt-get install -y sshpass

        # Check if runner service is running and fix if needed
        echo "🔍 Checking GitHub Actions runner service status..."
        SERVICE_STATUS=$(sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "cd ~/actions-runner && ./svc.sh status" 2>/dev/null || echo "FAILED")

        if echo "$SERVICE_STATUS" | grep -q "Started\|Running"; then
          echo "✅ Runner service is already running"
        else
          echo "⚠️ Runner service not running, starting runner directly..."
          # Start runner directly in background
          sshpass -p "$SSH_PASS" ssh $SSH_OPTIONS $SSH_USER@$SSH_HOST "cd ~/actions-runner && nohup ./run.sh > runner.log 2>&1 &"
          sleep 15
        fi

        echo "✅ Wake-up and runner configuration completed"
        echo "runner-status=wake-completed" >> $GITHUB_OUTPUT

  build-watch-app:
    name: Watch App Build (Scaleway Mac)
    runs-on: [self-hosted, macos]
    needs: [setup, check-runner-availability, wake-scaleway-runner]
    outputs:
      watch-start-time: ${{ steps.watch-start-time.outputs.watch-start-time }}
      build-start-time: ${{ steps.build_watch.outputs.build-start-time }}
      build-end-time: ${{ steps.build_watch.outputs.build-end-time }}
      build-duration: ${{ steps.build_watch.outputs.build-duration }}
      build-success: ${{ steps.build_watch.outputs.build-success }}
      ipa-found: ${{ steps.check_ipa.outputs.ipa-found }}
      testflight-upload-success: ${{ steps.upload_testflight.outputs.upload-success }}
      version-code: ${{ needs.setup.outputs.version-code }}

    steps:
    - name: Record Watch app job start time
      id: watch-start-time
      run: |
        WATCH_START_TIME=$(date +%s)
        echo "watch-start-time=$WATCH_START_TIME" >> $GITHUB_OUTPUT
        echo "Watch app build started at: $(date -r $WATCH_START_TIME)"

    - uses: actions/checkout@v4

    - name: Debug repository structure after checkout
      run: |
        echo "🔍 Debugging repository structure after checkout..."
        echo "Current working directory: $(pwd)"
        echo ""
        echo "Root directory contents:"
        ls -la
        echo ""
        echo "DrMuscleWatch directory structure (if exists):"
        if [ -d "DrMuscleWatch" ]; then
          find DrMuscleWatch -type f -name "*.xcodeproj" -o -name "Info.plist" | head -20
          echo ""
          echo "DrMuscleWatch directory tree (first 3 levels):"
          find DrMuscleWatch -maxdepth 3 -type d | sort
        else
          echo "❌ DrMuscleWatch directory does not exist!"
        fi
        echo ""
        echo "Looking for any .xcodeproj files in repository:"
        find . -name "*.xcodeproj" -type d | head -10

    - name: Validate Watch app project structure
      run: |
        echo "🔍 Validating Dr. Muscle Watch app project structure..."

        # Check if the project directory exists
        if [ ! -d "$PROJECT_PATH" ]; then
          echo "❌ Project directory not found: $PROJECT_PATH"
          exit 1
        fi

        # Check if the Xcode project exists (xcodeproj is a directory bundle)
        if [ ! -d "$PROJECT_PATH/$PROJECT_NAME" ]; then
          echo "❌ Xcode project not found: $PROJECT_PATH/$PROJECT_NAME"
          exit 1
        fi

        # Check if Info.plist exists
        INFO_PLIST_PATH="$PROJECT_PATH/DrMuscleWatchApp/Info.plist"
        if [ ! -f "$INFO_PLIST_PATH" ]; then
          echo "❌ Info.plist not found: $INFO_PLIST_PATH"
          exit 1
        fi

        echo "✅ Watch app project structure validated successfully"
        echo "  Project: $PROJECT_PATH/$PROJECT_NAME"
        echo "  Scheme: $SCHEME_NAME"
        echo "  Info.plist: $INFO_PLIST_PATH"

    - name: Set App Version in Info.plist
      run: |
        # Use version from setup job
        CLEAN_VERSION="${{ needs.setup.outputs.version-code }}"

        echo "🔧 Setting Watch app version: $CLEAN_VERSION"

        # Update Info.plist for the standalone Watch app
        INFO_PLIST_PATH="$PROJECT_PATH/DrMuscleWatchApp/Info.plist"
        if [ -f "$INFO_PLIST_PATH" ]; then
          echo "Found Info.plist at: $INFO_PLIST_PATH"

          # Show current content before update
          echo "Current version content:"
          grep -A1 "CFBundleShortVersionString\|CFBundleVersion" "$INFO_PLIST_PATH" || echo "No version keys found"

          # Create backup
          cp "$INFO_PLIST_PATH" "${INFO_PLIST_PATH}.bak"

          # Update both CFBundleShortVersionString and CFBundleVersion with same 3-part version
          plutil -replace CFBundleShortVersionString -string "$CLEAN_VERSION" "$INFO_PLIST_PATH"
          plutil -replace CFBundleVersion -string "$CLEAN_VERSION" "$INFO_PLIST_PATH"

          echo "✅ Successfully updated $INFO_PLIST_PATH"
          echo "Updated version content:"
          grep -A1 "CFBundleShortVersionString\|CFBundleVersion" "$INFO_PLIST_PATH"

          # Verify the update was successful
          if grep -q "$CLEAN_VERSION" "$INFO_PLIST_PATH"; then
            echo "✅ Version update verification successful"
          else
            echo "❌ Version update verification failed!"
            exit 1
          fi
        else
          echo "❌ Watch app Info.plist not found at $INFO_PLIST_PATH"
          exit 1
        fi

    - name: Clean up existing keychain
      run: |
        # Remove existing signing keychain if it exists (for self-hosted runners)
        if security list-keychains | grep -q "signing_temp.keychain"; then
          security delete-keychain signing_temp.keychain
          echo "🧹 Removed existing signing_temp.keychain"
        else
          echo "✅ No existing signing keychain to clean up"
        fi

        # Also check for keychain files in the filesystem
        if [ -f "$HOME/Library/Keychains/signing_temp.keychain-db" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain-db"
          echo "🧹 Removed signing_temp.keychain-db file"
        fi

        if [ -f "$HOME/Library/Keychains/signing_temp.keychain" ]; then
          rm -f "$HOME/Library/Keychains/signing_temp.keychain"
          echo "🧹 Removed signing_temp.keychain file"
        fi

    - name: Install Apple Intermediate Certificate
      run: |
        # Download and install Apple Intermediate Certificate Authority for complete certificate chain
        echo "📥 Downloading Apple Intermediate Certificate Authority..."
        curl -o AppleWWDRCAG3.cer "https://www.apple.com/certificateauthority/AppleWWDRCAG3.cer"

        # Import intermediate certificate into login keychain (no sudo required)
        echo "🔧 Importing intermediate certificate into login keychain..."
        if security import AppleWWDRCAG3.cer -k login.keychain -T /usr/bin/codesign; then
          echo "✅ Apple Intermediate Certificate Authority imported successfully"
        else
          echo "ℹ️ Certificate already exists in keychain (this is fine)"
        fi

        echo "✅ Apple Intermediate Certificate Authority is available in login keychain"

    - name: Import Code-Signing Certificates
      uses: apple-actions/import-codesign-certs@v3
      with:
        p12-file-base64: "${{ secrets.P12_CERTIFICATE }}"
        p12-password: "${{ secrets.P12_CERTIFICATE_PASSWORD }}"
        keychain-password: ""
        create-keychain: true

    - name: Setup Watch App Provisioning Profile
      id: setup-provisioning
      run: |
        # Create the provisioning profiles directory if it doesn't exist
        mkdir -p ~/Library/MobileDevice/Provisioning\ Profiles/

        # Check if the secret exists and is not empty
        if [ -z "${{ secrets.WATCH_APP_PROVISIONING_PROFILE_BASE64 }}" ]; then
          echo "❌ WATCH_APP_PROVISIONING_PROFILE_BASE64 secret is not set or empty"
          echo "ℹ️ Switching to automatic code signing"
          echo "use-automatic-signing=true" >> $GITHUB_OUTPUT
        else
          # Decode the base64-encoded provisioning profile from secrets and save it
          echo "${{ secrets.WATCH_APP_PROVISIONING_PROFILE_BASE64 }}" | base64 --decode > ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision

          # Verify the profile was created successfully and has content
          if [ -f ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision ]; then
          PROFILE_SIZE=$(stat -f%z ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision)
          if [ "$PROFILE_SIZE" -gt 100 ]; then
            echo "✅ Watch app provisioning profile installed successfully (${PROFILE_SIZE} bytes)"

            # Try to extract UUID from the profile to validate it
            if plutil -extract UUID xml1 -o - ~/Library/MobileDevice/Provisioning\ Profiles/watch_profile.mobileprovision 2>/dev/null | grep -q "string"; then
              echo "✅ Provisioning profile contains valid UUID"
              echo "use-automatic-signing=false" >> $GITHUB_OUTPUT
            else
              echo "⚠️ Provisioning profile missing UUID, switching to automatic signing"
              echo "use-automatic-signing=true" >> $GITHUB_OUTPUT
            fi
          else
            echo "❌ Provisioning profile file is too small (${PROFILE_SIZE} bytes), likely corrupted"
            echo "ℹ️ Switching to automatic code signing"
            echo "use-automatic-signing=true" >> $GITHUB_OUTPUT
          fi
          else
            echo "❌ Failed to install Watch app provisioning profile"
            echo "ℹ️ Switching to automatic code signing"
            echo "use-automatic-signing=true" >> $GITHUB_OUTPUT
          fi
        fi

    - name: Pre-SDK Installation Diagnostics
      run: |
        echo "🔍 PRE-SDK DIAGNOSTICS - This step should always run"
        echo "Current working directory: $(pwd)"
        echo "Environment variables:"
        echo "  PROJECT_PATH: $PROJECT_PATH"
        echo "  PROJECT_NAME: $PROJECT_NAME"
        echo "  SCHEME_NAME: $SCHEME_NAME"
        echo "User: $(whoami)"
        echo "Date: $(date)"
        echo "✅ Pre-SDK diagnostics completed"

    - name: Install watchOS SDK and Simulators
      run: |
        echo "� SDK INSTALLATION STEP STARTED"
        echo "Xcode version: $(xcodebuild -version)"
        echo "Xcode path: $(xcode-select -p)"

        echo "📋 Currently installed SDKs:"
        xcodebuild -showsdks

        echo "� Checking for watchOS SDK..."
        if xcodebuild -showsdks | grep -i watchos; then
          echo "✅ watchOS SDK found"
        else
          echo "❌ watchOS SDK not found"

          echo "� Attempting to install watchOS SDK..."
          echo "🔄 Trying: xcodebuild -downloadPlatform watchOS"
          xcodebuild -downloadPlatform watchOS || echo "⚠️ downloadPlatform failed"

          echo "� Trying: xcodebuild -runFirstLaunch"
          xcodebuild -runFirstLaunch || echo "⚠️ runFirstLaunch failed"

          echo "⏳ Waiting 15 seconds for installation..."
          sleep 15

          echo "📋 SDKs after installation attempt:"
          xcodebuild -showsdks
        fi

        echo "🔍 Checking simulator runtimes:"
        xcrun simctl list runtimes | grep -i watchos || echo "No watchOS runtimes found"

        echo "🔍 Checking available simulators:"
        xcrun simctl list devices | grep -i watch || echo "No Watch simulators found"

        echo "✅ SDK installation step completed"

    - name: Create ExportOptions.plist for TestFlight
      run: |
        # Change to project directory to create ExportOptions.plist where it will be used
        cd "$PROJECT_PATH"
        cat << EOF > ExportOptions.plist
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>method</key>
            <string>app-store</string>
            <key>teamID</key>
            <string>${{ secrets.APPLE_TEAM_ID }}</string>
            <key>uploadSymbols</key>
            <true/>
            <key>signingStyle</key>
            <string>manual</string>
            <key>provisioningProfiles</key>
            <dict>
                <key>${WATCH_APP_BUNDLE_ID}</key>
                <string>${WATCH_APP_PROVISIONING_PROFILE_NAME}</string>
            </dict>
        </dict>
        </plist>
        EOF
        echo "✅ ExportOptions.plist created for standalone Watch app."
        cat ExportOptions.plist

    - name: Build and Archive Watch App
      id: build_watch
      run: |
        # Record build start time
        BUILD_START_TIME=$(date +%s)
        echo "build-start-time=$BUILD_START_TIME" >> $GITHUB_OUTPUT
        echo "Watch app build compilation started at: $(date -r $BUILD_START_TIME)"

        # Change to project directory
        cd "$PROJECT_PATH"

        echo "🔨 Starting Watch app archive..."
        echo "  Project: $PROJECT_NAME"
        echo "  Scheme: $SCHEME_NAME"
        echo "  Configuration: Release"

        # Create build directory
        mkdir -p build

        # Determine signing style based on provisioning profile availability
        USE_AUTO_SIGNING="${{ steps.setup-provisioning.outputs.use-automatic-signing }}"
        if [ "$USE_AUTO_SIGNING" = "true" ]; then
          echo "📝 Using automatic code signing"
          CODE_SIGN_STYLE="Automatic"
          TEAM_SETTING="DEVELOPMENT_TEAM=${{ secrets.APPLE_TEAM_ID }}"
          PROFILE_SETTING=""
        else
          echo "📝 Using manual code signing with provisioning profile"
          CODE_SIGN_STYLE="Manual"
          TEAM_SETTING="DEVELOPMENT_TEAM=${{ secrets.APPLE_TEAM_ID }}"
          PROFILE_SETTING="PROVISIONING_PROFILE_SPECIFIER=${WATCH_APP_PROVISIONING_PROFILE_NAME}"
        fi

        # Try different destination approaches for watchOS build
        echo "🎯 Attempting build with different destination strategies..."

        # Strategy 1: Generic watchOS destination
        echo "📱 Strategy 1: Generic watchOS destination"
        if xcodebuild archive \
          -project "${PROJECT_NAME}" \
          -scheme "${SCHEME_NAME}" \
          -configuration Release \
          -destination "generic/platform=watchOS" \
          -archivePath "build/${APP_NAME}.xcarchive" \
          CODE_SIGN_STYLE="$CODE_SIGN_STYLE" \
          $TEAM_SETTING \
          PRODUCT_BUNDLE_IDENTIFIER="${WATCH_APP_BUNDLE_ID}" \
          $PROFILE_SETTING 2>&1; then
          echo "✅ Build successful with generic watchOS destination"
        else
          echo "❌ Generic watchOS destination failed, trying alternative..."

          # Strategy 2: Try without explicit destination
          echo "📱 Strategy 2: Build without explicit destination"
          if xcodebuild archive \
            -project "${PROJECT_NAME}" \
            -scheme "${SCHEME_NAME}" \
            -configuration Release \
            -archivePath "build/${APP_NAME}.xcarchive" \
            CODE_SIGN_STYLE="$CODE_SIGN_STYLE" \
            $TEAM_SETTING \
            PRODUCT_BUNDLE_IDENTIFIER="${WATCH_APP_BUNDLE_ID}" \
            $PROFILE_SETTING 2>&1; then
            echo "✅ Build successful without explicit destination"
          else
            echo "❌ Build without destination failed, trying simulator destination..."

            # Strategy 3: Try with watchOS simulator destination
            echo "📱 Strategy 3: watchOS Simulator destination"
            WATCHOS_SIM=$(xcrun simctl list devices available | grep "Apple Watch" | head -n 1 | sed 's/.*(\([^)]*\)).*/\1/' || echo "")
            if [ -n "$WATCHOS_SIM" ]; then
              echo "Found watchOS simulator: $WATCHOS_SIM"
              if xcodebuild archive \
                -project "${PROJECT_NAME}" \
                -scheme "${SCHEME_NAME}" \
                -configuration Release \
                -destination "id=$WATCHOS_SIM" \
                -archivePath "build/${APP_NAME}.xcarchive" \
                CODE_SIGN_STYLE="$CODE_SIGN_STYLE" \
                $TEAM_SETTING \
                PRODUCT_BUNDLE_IDENTIFIER="${WATCH_APP_BUNDLE_ID}" \
                $PROFILE_SETTING 2>&1; then
                echo "✅ Build successful with simulator destination"
              else
                echo "❌ All build strategies failed"
                exit 1
              fi
            else
              echo "❌ No watchOS simulator found, build failed"
              exit 1
            fi
          fi
        fi

        # Record build end time and calculate duration
        BUILD_END_TIME=$(date +%s)
        echo "build-end-time=$BUILD_END_TIME" >> $GITHUB_OUTPUT
        BUILD_DURATION=$((BUILD_END_TIME - BUILD_START_TIME))
        echo "build-duration=$BUILD_DURATION" >> $GITHUB_OUTPUT
        echo "Watch app build compilation completed at: $(date -r $BUILD_END_TIME)"
        echo "Build duration: $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s"

        # Check if build was successful
        BUILD_RESULT=$?
        if [ $BUILD_RESULT -eq 0 ]; then
          echo "build-success=true" >> $GITHUB_OUTPUT
          echo "✅ Build completed successfully"
        else
          echo "build-success=false" >> $GITHUB_OUTPUT
          echo "❌ Build failed with exit code $BUILD_RESULT"
          exit 1
        fi

    - name: Export IPA for TestFlight
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"

        echo "📦 Exporting IPA for TestFlight..."
        xcodebuild -exportArchive \
          -archivePath "build/${APP_NAME}.xcarchive" \
          -exportPath "build/ipa" \
          -exportOptionsPlist ExportOptions.plist
        echo "✅ IPA export completed."

        # List exported files
        echo "📋 Exported files:"
        ls -la build/ipa/

    - name: Check if IPA file exists
      id: check_ipa
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"

        # Look for IPA file in export directory
        IPA_PATH=$(find build/ipa -name "*.ipa" | head -n 1)
        if [ -n "$IPA_PATH" ] && [ -f "$IPA_PATH" ]; then
          echo "✅ IPA file exists: $IPA_PATH"
          echo "ipa-found=true" >> $GITHUB_OUTPUT
          echo "ipa-path=$IPA_PATH" >> $GITHUB_OUTPUT

          # Get file size
          IPA_SIZE=$(stat -f%z "$IPA_PATH")
          IPA_SIZE_MB=$(echo "scale=2; $IPA_SIZE / 1024 / 1024" | bc -l)
          echo "📦 IPA Size: ${IPA_SIZE_MB} MB (${IPA_SIZE} bytes)"
        else
          echo "❌ IPA file is missing!"
          echo "ipa-found=false" >> $GITHUB_OUTPUT
          exit 1
        fi

    - name: Install App Store Connect API Key
      run: |
        mkdir -p ~/private_keys
        echo -n "${{ secrets.APPSTORE_API_PRIVATE_KEY }}" | base64 --decode --output ~/private_keys/AuthKey_${{ secrets.APPSTORE_API_KEY_ID }}.p8
        echo "✅ App Store Connect API Key installed."

    - name: Upload to TestFlight
      id: upload_testflight
      env:
        API_KEY: ${{ secrets.APPSTORE_API_KEY_ID }}
        API_ISSUER: ${{ secrets.APPSTORE_ISSUER_ID }}
      run: |
        # Change to project directory
        cd "$PROJECT_PATH"

        IPA_PATH="${{ steps.check_ipa.outputs.ipa-path }}"
        echo "🚀 Uploading Watch app to TestFlight..."
        echo "  IPA: $IPA_PATH"

        if xcrun altool --upload-app -f "$IPA_PATH" -t watchos --apiKey $API_KEY --apiIssuer $API_ISSUER; then
          echo "upload-success=true" >> $GITHUB_OUTPUT
          echo "✅ Successfully uploaded to TestFlight"
        else
          echo "upload-success=false" >> $GITHUB_OUTPUT
          echo "❌ Failed to upload to TestFlight"
          exit 1
        fi

    # Output Watch app build summary
    - name: Output Watch App Build Summary
      if: always()
      run: |
        echo "### ⌚ Dr. Muscle Watch App Build Summary" >> $GITHUB_STEP_SUMMARY
        echo "- **Project:** $PROJECT_PATH/$PROJECT_NAME" >> $GITHUB_STEP_SUMMARY
        echo "- **Scheme:** $SCHEME_NAME" >> $GITHUB_STEP_SUMMARY
        echo "- **Bundle ID:** $WATCH_APP_BUNDLE_ID" >> $GITHUB_STEP_SUMMARY

        # Add performance metrics
        if [ -n "${{ steps.build_watch.outputs.build-duration }}" ]; then
          BUILD_DURATION="${{ steps.build_watch.outputs.build-duration }}"
          echo "- **Build Duration:** $(($BUILD_DURATION / 60))m $(($BUILD_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi

        # Add build status information
        if [ "${{ steps.build_watch.outputs.build-success }}" = "true" ]; then
          echo "- **Build:** ✅ Completed successfully" >> $GITHUB_STEP_SUMMARY
        else
          echo "- **Build:** ❌ Failed" >> $GITHUB_STEP_SUMMARY
        fi

        # Add package information if found
        if [ "${{ steps.check_ipa.outputs.ipa-found }}" = "true" ]; then
          echo "- **Package:** ✅ Built successfully" >> $GITHUB_STEP_SUMMARY
          echo "- **Package Path:** \`${{ steps.check_ipa.outputs.ipa-path }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Version:** ${{ needs.setup.outputs.version-name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **watchOS Target:** watchOS 9.0+" >> $GITHUB_STEP_SUMMARY

          # Add deployment information
          if [ "${{ steps.upload_testflight.outputs.upload-success }}" = "true" ]; then
            echo "- **Deployment:** ✅ Uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "- **Distribution:** TestFlight (Internal Testing)" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **Deployment:** ⚠️ Not uploaded to TestFlight" >> $GITHUB_STEP_SUMMARY
            echo "  - ℹ️ Reason: Upload failed or credentials missing" >> $GITHUB_STEP_SUMMARY
          fi
        else
          echo "- **Package:** ❌ Build failed, no package was generated" >> $GITHUB_STEP_SUMMARY
        fi

        # Add workflow timing
        if [ -n "${{ needs.setup.outputs.start-time }}" ] && [ -n "${{ steps.watch-start-time.outputs.watch-start-time }}" ]; then
          START_TIME="${{ needs.setup.outputs.start-time }}"
          CURRENT_TIME=$(date +%s)
          TOTAL_DURATION=$((CURRENT_TIME - START_TIME))
          echo "- **Total Workflow Runtime:** $(($TOTAL_DURATION / 60))m $(($TOTAL_DURATION % 60))s" >> $GITHUB_STEP_SUMMARY
        fi